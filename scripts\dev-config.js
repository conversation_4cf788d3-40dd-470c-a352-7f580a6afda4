/**
 * 开发环境配置
 * 用于管理开发时的环境变量和路径配置
 */

const path = require('path')

// 项目根目录
const ROOT_DIR = path.resolve(__dirname, '..')

// 各个包的路径
const PATHS = {
  root: ROOT_DIR,
  main: path.join(ROOT_DIR, 'apps/main'),
  renderer: path.join(ROOT_DIR, 'apps/renderer'),
  preload: path.join(ROOT_DIR, 'packages/preload'),
  shared: path.join(ROOT_DIR, 'packages/shared')
}

// 构建输出路径
const BUILD_PATHS = {
  main: path.join(PATHS.main, 'dist'),
  renderer: path.join(PATHS.renderer, 'dist'),
  preload: path.join(PATHS.preload, 'dist'),
  shared: path.join(PATHS.shared, 'dist')
}

// 开发服务器配置
const DEV_CONFIG = {
  renderer: {
    port: 5173,
    host: 'localhost'
  }
}

// 环境变量配置
const ENV_VARS = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  ELECTRON_RENDERER_URL: `http://${DEV_CONFIG.renderer.host}:${DEV_CONFIG.renderer.port}`
}

module.exports = {
  PATHS,
  BUILD_PATHS,
  DEV_CONFIG,
  ENV_VARS
}
