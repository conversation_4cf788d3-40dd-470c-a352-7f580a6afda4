import React, { useState } from 'react'

const MainContent: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])

  const handleOpenFile = async () => {
    try {
      const result = await window.electronAPI.dialog.openFile({
        properties: ['openFile', 'multiSelections'],
        filters: [
          { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
          { name: '所有文件', extensions: ['*'] }
        ]
      })

      if (result.success && result.data) {
        setSelectedFiles(result.data)
        console.log('选择的文件:', result.data)
      }
    } catch (error) {
      console.error('打开文件失败:', error)
      await window.electronAPI.dialog.showMessageBox({
        type: 'error',
        title: '错误',
        message: '打开文件失败',
        detail: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  const handleShowAbout = async () => {
    try {
      const versionResult = await window.electronAPI.app.getVersion()
      const version = versionResult.success ? versionResult.data : '未知'
      
      await window.electronAPI.dialog.showMessageBox({
        type: 'info',
        title: '关于 Fast OCR',
        message: 'Fast OCR',
        detail: `版本: ${version}\n\n一个基于 Electron + React + TypeScript 的现代化 OCR 应用程序。\n\n开发者: jf`
      })
    } catch (error) {
      console.error('显示关于信息失败:', error)
    }
  }

  return (
    <div className="main-content">
      <div className="welcome-section">
        <h1>🚀 欢迎使用 Fast OCR</h1>
        <p>
          这是一个现代化的 OCR（光学字符识别）应用程序，基于 Electron、React 和 TypeScript 构建。
          <br />
          选择图片文件开始进行文字识别，或者探索应用程序的其他功能。
        </p>
        
        <div className="action-buttons">
          <button className="action-button" onClick={handleOpenFile}>
            📁 选择图片文件
          </button>
          <button className="action-button secondary" onClick={handleShowAbout}>
            ℹ️ 关于应用
          </button>
        </div>

        {selectedFiles.length > 0 && (
          <div style={{ marginTop: '32px', width: '100%' }}>
            <h3 style={{ marginBottom: '16px', color: 'var(--text-primary)' }}>
              已选择的文件:
            </h3>
            <div style={{ 
              background: 'var(--surface-color)', 
              border: '1px solid var(--border-color)',
              borderRadius: 'var(--border-radius)',
              padding: '16px',
              maxHeight: '200px',
              overflowY: 'auto'
            }}>
              {selectedFiles.map((file, index) => (
                <div 
                  key={index}
                  style={{ 
                    padding: '8px 0',
                    borderBottom: index < selectedFiles.length - 1 ? '1px solid var(--border-color)' : 'none',
                    fontSize: '14px',
                    color: 'var(--text-secondary)',
                    wordBreak: 'break-all'
                  }}
                >
                  {file}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MainContent
