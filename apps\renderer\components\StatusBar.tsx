import React from 'react'

interface StatusBarProps {
  version: string
  platform: string
}

const StatusBar: React.FC<StatusBarProps> = ({ version, platform }) => {
  const getPlatformDisplayName = (platform: string): string => {
    switch (platform) {
      case 'win32':
        return 'Windows'
      case 'darwin':
        return 'macOS'
      case 'linux':
        return 'Linux'
      default:
        return platform
    }
  }

  return (
    <div className="status-bar">
      <div className="status-left">
        <span>就绪</span>
      </div>
      <div className="status-right">
        <span>版本 {version}</span>
        <span>•</span>
        <span>{getPlatformDisplayName(platform)}</span>
      </div>
    </div>
  )
}

export default StatusBar
