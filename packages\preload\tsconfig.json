{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "Node", "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "baseUrl": ".", "paths": {"@shared/*": ["../shared/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"]}