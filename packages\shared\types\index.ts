// 共享类型定义

// IPC 通信相关类型
export interface IpcChannels {
  // 主进程到渲染进程
  MAIN_TO_RENDERER: {
    APP_READY: 'app:ready'
    WINDOW_FOCUS: 'window:focus'
    WINDOW_BLUR: 'window:blur'
  }
  
  // 渲染进程到主进程
  RENDERER_TO_MAIN: {
    GET_APP_VERSION: 'app:get-version'
    MINIMIZE_WINDOW: 'window:minimize'
    MAXIMIZE_WINDOW: 'window:maximize'
    CLOSE_WINDOW: 'window:close'
    OPEN_FILE_DIALOG: 'dialog:open-file'
    SHOW_MESSAGE_BOX: 'dialog:message-box'
  }
}

// 应用程序状态
export interface AppState {
  version: string
  isMaximized: boolean
  isFullscreen: boolean
  theme: 'light' | 'dark' | 'system'
}

// 文件相关类型
export interface FileInfo {
  path: string
  name: string
  size: number
  lastModified: Date
  type: string
}

// OCR 相关类型（为将来的 OCR 功能预留）
export interface OcrResult {
  text: string
  confidence: number
  boundingBox: {
    x: number
    y: number
    width: number
    height: number
  }
}

export interface OcrOptions {
  language: string
  outputFormat: 'text' | 'json' | 'xml'
  preprocessImage: boolean
}

// 窗口相关类型
export interface WindowBounds {
  x: number
  y: number
  width: number
  height: number
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}
