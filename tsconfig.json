{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@shared/*": ["src/shared/*"], "@renderer/*": ["src/renderer/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"]}}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist", "dist-electron", "out"], "references": [{"path": "./src/main/tsconfig.json"}, {"path": "./src/renderer/tsconfig.json"}, {"path": "./src/preload/tsconfig.json"}]}