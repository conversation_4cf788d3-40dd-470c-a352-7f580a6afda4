{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@fast-ocr/shared": ["packages/shared"], "@fast-ocr/shared/*": ["packages/shared/*"], "@fast-ocr/preload": ["packages/preload"], "@fast-ocr/preload/*": ["packages/preload/*"]}}, "include": ["scripts/**/*"], "exclude": ["node_modules", "apps/*/dist", "packages/*/dist", "out"], "references": [{"path": "./apps/main/tsconfig.json"}, {"path": "./apps/renderer/tsconfig.json"}, {"path": "./packages/preload/tsconfig.json"}, {"path": "./packages/shared/tsconfig.json"}]}