#!/usr/bin/env node

/**
 * 开发环境启动脚本
 * 并行启动渲染进程开发服务器、主进程和preload脚本的监听编译
 */

const { spawn } = require('child_process')
const path = require('path')
const { ENV_VARS, PATHS } = require('./dev-config')

// 设置环境变量
process.env.NODE_ENV = 'development'
process.env.ELECTRON_RENDERER_URL = ENV_VARS.ELECTRON_RENDERER_URL

console.log('🚀 启动Fast OCR开发环境...\n')

// 启动渲染进程开发服务器
console.log('📦 启动React渲染进程开发服务器...')
const rendererProcess = spawn('npm', ['run', 'dev'], {
  cwd: PATHS.renderer,
  stdio: 'inherit',
  shell: true
})

// 启动preload脚本监听编译
console.log('🔧 启动Preload脚本监听编译...')
const preloadProcess = spawn('npm', ['run', 'dev'], {
  cwd: PATHS.preload,
  stdio: 'inherit',
  shell: true
})

// 启动主进程监听编译
console.log('⚡ 启动Electron主进程监听编译...')
const mainProcess = spawn('npm', ['run', 'dev'], {
  cwd: PATHS.main,
  stdio: 'inherit',
  shell: true
})

// 等待一段时间后启动Electron
setTimeout(() => {
  console.log('🖥️  启动Electron应用...')
  const electronProcess = spawn('npx', ['electron', PATHS.main], {
    cwd: PATHS.root,
    stdio: 'inherit',
    shell: true,
    env: {
      ...process.env,
      NODE_ENV: 'development',
      ELECTRON_RENDERER_URL: ENV_VARS.ELECTRON_RENDERER_URL
    }
  })

  // 处理进程退出
  electronProcess.on('close', () => {
    console.log('\n🛑 Electron应用已关闭，停止所有开发服务...')
    rendererProcess.kill()
    preloadProcess.kill()
    mainProcess.kill()
    process.exit(0)
  })
}, 3000)

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，停止所有开发服务...')
  rendererProcess.kill()
  preloadProcess.kill()
  mainProcess.kill()
  process.exit(0)
})

// 处理进程错误
rendererProcess.on('error', (err) => {
  console.error('❌ 渲染进程启动失败:', err)
})

preloadProcess.on('error', (err) => {
  console.error('❌ Preload脚本编译失败:', err)
})

mainProcess.on('error', (err) => {
  console.error('❌ 主进程编译失败:', err)
})
