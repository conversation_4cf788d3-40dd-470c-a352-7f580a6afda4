#!/usr/bin/env node

/**
 * 生产环境构建脚本
 * 按正确顺序构建所有包
 */

const { execSync } = require('child_process')
const path = require('path')
const fs = require('fs')
const { PATHS, BUILD_PATHS } = require('./dev-config')

console.log('🏗️  开始构建Fast OCR生产版本...\n')

// 清理所有构建目录
console.log('🧹 清理构建目录...')
Object.values(BUILD_PATHS).forEach(buildPath => {
  if (fs.existsSync(buildPath)) {
    execSync(`rimraf "${buildPath}"`, { stdio: 'inherit' })
  }
})

try {
  // 1. 构建共享包
  console.log('📦 构建共享包...')
  execSync('npm run build', {
    cwd: PATHS.shared,
    stdio: 'inherit'
  })

  // 2. 构建preload脚本
  console.log('🔧 构建Preload脚本...')
  execSync('npm run build', {
    cwd: PATHS.preload,
    stdio: 'inherit'
  })

  // 3. 构建主进程
  console.log('⚡ 构建Electron主进程...')
  execSync('npm run build', {
    cwd: PATHS.main,
    stdio: 'inherit'
  })

  // 4. 构建渲染进程
  console.log('🎨 构建React渲染进程...')
  execSync('npm run build', {
    cwd: PATHS.renderer,
    stdio: 'inherit'
  })

  console.log('\n✅ 所有包构建完成！')
  console.log('📁 构建输出目录:')
  Object.entries(BUILD_PATHS).forEach(([name, buildPath]) => {
    console.log(`   ${name}: ${buildPath}`)
  })

} catch (error) {
  console.error('\n❌ 构建失败:', error.message)
  process.exit(1)
}
