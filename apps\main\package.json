{"name": "@fast-ocr/main", "version": "1.0.0", "description": "Fast OCR Electron主进程", "main": "dist/index.js", "type": "commonjs", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"electron-squirrel-startup": "^1.0.1"}, "devDependencies": {"@types/node": "^24.1.0", "electron": "37.2.4", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "peerDependencies": {"@fast-ocr/shared": "workspace:*", "@fast-ocr/preload": "workspace:*"}}