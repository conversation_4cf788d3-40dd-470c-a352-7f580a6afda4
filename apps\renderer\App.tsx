import React, { useState, useEffect } from 'react'
import { AppState } from '@shared/types'
import TitleBar from './components/TitleBar'
import MainContent from './components/MainContent'
import StatusBar from './components/StatusBar'

const App: React.FC = () => {
  const [appState, setAppState] = useState<AppState | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 初始化应用状态
    const initializeApp = async () => {
      try {
        // 检查 electronAPI 是否可用
        if (!window.electronAPI) {
          throw new Error('Electron API 不可用')
        }

        // 获取应用状态
        const stateResponse = await window.electronAPI.app.getState()
        if (stateResponse.success && stateResponse.data) {
          setAppState(stateResponse.data)
        } else {
          throw new Error(stateResponse.error || '获取应用状态失败')
        }

        setIsLoading(false)
      } catch (err) {
        console.error('应用初始化失败:', err)
        setError(err instanceof Error ? err.message : '未知错误')
        setIsLoading(false)
      }
    }

    initializeApp()
  }, [])

  // 窗口控制函数
  const handleMinimize = async () => {
    try {
      await window.electronAPI.window.minimize()
    } catch (err) {
      console.error('最小化窗口失败:', err)
    }
  }

  const handleMaximize = async () => {
    try {
      await window.electronAPI.window.maximize()
    } catch (err) {
      console.error('最大化窗口失败:', err)
    }
  }

  const handleClose = async () => {
    try {
      await window.electronAPI.window.close()
    } catch (err) {
      console.error('关闭窗口失败:', err)
    }
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner"></div>
        <p>正在初始化应用...</p>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className="app-error">
        <h2>应用启动失败</h2>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          重新加载
        </button>
      </div>
    )
  }

  return (
    <div className="app">
      <TitleBar
        title="Fast OCR"
        onMinimize={handleMinimize}
        onMaximize={handleMaximize}
        onClose={handleClose}
        isMaximized={appState?.isMaximized || false}
      />
      <MainContent />
      <StatusBar
        version={appState?.version || ''}
        platform={window.electronAPI?.platform || 'unknown'}
      />
    </div>
  )
}

export default App
