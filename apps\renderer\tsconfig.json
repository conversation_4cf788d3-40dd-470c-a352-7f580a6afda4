{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client"], "baseUrl": ".", "paths": {"@/*": ["./*"], "@shared/*": ["../../packages/shared/*"], "@components/*": ["./components/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.d.ts"], "exclude": ["node_modules", "dist"]}