import { app, BrowserWindow, ipcMain, dialog, shell } from 'electron'
import { join } from 'path'
import { AppState, ApiResponse } from '@shared/types'

// 处理 Windows 安装时的快捷方式创建/删除
if (require('electron-squirrel-startup')) {
  app.quit()
}

class MainWindow {
  private window: BrowserWindow | null = null
  private appState: AppState = {
    version: app.getVersion(),
    isMaximized: false,
    isFullscreen: false,
    theme: 'system'
  }

  constructor() {
    this.createWindow()
    this.setupIpcHandlers()
  }

  private createWindow(): void {
    // 创建浏览器窗口
    this.window = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      autoHideMenuBar: true,
      titleBarStyle: 'default',
      webPreferences: {
        preload: join(__dirname, '../preload/index.js'),
        sandbox: false,
        contextIsolation: true,
        nodeIntegration: false
      }
    })

    // 窗口准备好后显示
    this.window.on('ready-to-show', () => {
      this.window?.show()
      
      // 开发环境下打开开发者工具
      if (process.env.NODE_ENV === 'development') {
        this.window?.webContents.openDevTools()
      }
    })

    // 窗口关闭时的处理
    this.window.on('closed', () => {
      this.window = null
    })

    // 监听窗口状态变化
    this.window.on('maximize', () => {
      this.appState.isMaximized = true
    })

    this.window.on('unmaximize', () => {
      this.appState.isMaximized = false
    })

    this.window.on('enter-full-screen', () => {
      this.appState.isFullscreen = true
    })

    this.window.on('leave-full-screen', () => {
      this.appState.isFullscreen = false
    })

    // 加载应用页面
    if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
      this.window.loadURL(process.env['ELECTRON_RENDERER_URL'])
    } else {
      this.window.loadFile(join(__dirname, '../renderer/index.html'))
    }

    // 处理外部链接
    this.window.webContents.setWindowOpenHandler((details) => {
      shell.openExternal(details.url)
      return { action: 'deny' }
    })
  }

  private setupIpcHandlers(): void {
    // 获取应用版本
    ipcMain.handle('app:get-version', (): ApiResponse<string> => {
      return {
        success: true,
        data: this.appState.version
      }
    })

    // 获取应用状态
    ipcMain.handle('app:get-state', (): ApiResponse<AppState> => {
      return {
        success: true,
        data: this.appState
      }
    })

    // 窗口控制
    ipcMain.handle('window:minimize', (): ApiResponse => {
      try {
        this.window?.minimize()
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    })

    ipcMain.handle('window:maximize', (): ApiResponse => {
      try {
        if (this.window?.isMaximized()) {
          this.window.unmaximize()
        } else {
          this.window?.maximize()
        }
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    })

    ipcMain.handle('window:close', (): ApiResponse => {
      try {
        this.window?.close()
        return { success: true }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    })

    // 文件对话框
    ipcMain.handle('dialog:open-file', async (_, options?: Electron.OpenDialogOptions): Promise<ApiResponse<string[]>> => {
      try {
        if (!this.window) {
          return {
            success: false,
            error: 'Window not available'
          }
        }

        const result = await dialog.showOpenDialog(this.window, {
          properties: ['openFile', 'multiSelections'],
          filters: [
            { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
            { name: 'All Files', extensions: ['*'] }
          ],
          ...options
        })

        if (result.canceled) {
          return {
            success: false,
            message: 'User canceled'
          }
        }

        return {
          success: true,
          data: result.filePaths
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    })

    // 消息框
    ipcMain.handle('dialog:message-box', async (_, options: Electron.MessageBoxOptions): Promise<ApiResponse<number>> => {
      try {
        if (!this.window) {
          return {
            success: false,
            error: 'Window not available'
          }
        }

        const result = await dialog.showMessageBox(this.window, options)
        return {
          success: true,
          data: result.response
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    })
  }

  public getWindow(): BrowserWindow | null {
    return this.window
  }
}

// 应用程序实例
let mainWindow: MainWindow | null = null

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  // 为 Windows 设置应用用户模型 ID
  app.setAppUserModelId('com.electron.fast-ocr')

  // 创建主窗口
  mainWindow = new MainWindow()

  // macOS 特定行为
  app.on('activate', function () {
    // 在 macOS 上，当点击 dock 图标且没有其他窗口打开时，
    // 通常会重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) {
      mainWindow = new MainWindow()
    }
  })
})

// 当所有窗口都关闭时退出应用程序，除了在 macOS 上
// 在 macOS 上，应用程序和它们的菜单栏通常保持活动状态，
// 直到用户使用 Cmd + Q 明确退出
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 在此文件中，您可以包含应用程序特定的主进程代码
// 您也可以将它们放在单独的文件中并在此处导入
