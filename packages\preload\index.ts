import { contextBridge, ipc<PERSON>enderer } from 'electron'

// Preload脚本内部类型定义 - 作为主进程和渲染进程的通信契约
interface AppState {
  version: string
  isMaximized: boolean
  isFullscreen: boolean
  theme: 'light' | 'dark' | 'system'
}

interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 定义暴露给渲染进程的 API
export interface ElectronAPI {
  // 应用程序相关
  app: {
    getVersion(): Promise<ApiResponse<string>>
    getState(): Promise<ApiResponse<AppState>>
  }
  
  // 窗口控制
  window: {
    minimize(): Promise<ApiResponse>
    maximize(): Promise<ApiResponse>
    close(): Promise<ApiResponse>
  }
  
  // 对话框
  dialog: {
    openFile(options?: Electron.OpenDialogOptions): Promise<ApiResponse<string[]>>
    showMessageBox(options: Electron.MessageBoxOptions): Promise<ApiResponse<number>>
  }
  
  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => void
  off: (channel: string, callback: (...args: any[]) => void) => void
  
  // 平台信息
  platform: NodeJS.Platform
  
  // 开发环境标识
  isDev: boolean
}

// 创建 API 对象
const electronAPI: ElectronAPI = {
  // 应用程序相关
  app: {
    getVersion: () => ipcRenderer.invoke('app:get-version'),
    getState: () => ipcRenderer.invoke('app:get-state')
  },
  
  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close')
  },
  
  // 对话框
  dialog: {
    openFile: (options) => ipcRenderer.invoke('dialog:open-file', options),
    showMessageBox: (options) => ipcRenderer.invoke('dialog:message-box', options)
  },
  
  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, callback)
  },
  
  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback)
  },
  
  // 平台信息
  platform: process.platform,
  
  // 开发环境标识
  isDev: process.env.NODE_ENV === 'development'
}

// 使用 contextBridge 安全地暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', electronAPI)
  } catch (error) {
    console.error('Failed to expose electronAPI:', error)
  }
} else {
  // 如果上下文隔离被禁用，直接挂载到 window 对象
  ;(window as any).electronAPI = electronAPI
}

// 类型声明，用于 TypeScript 支持
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
