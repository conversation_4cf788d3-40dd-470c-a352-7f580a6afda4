{"name": "fast-ocr", "productName": "fast-ocr", "version": "1.0.0", "description": "My Electron application description", "main": "dist-electron/main/index.js", "scripts": {"dev": "vite", "build": "npm run build:renderer && npm run build:electron", "build:renderer": "cd src/renderer && vite build --outDir ../../dist-electron/renderer --emptyOutDir", "build:electron": "tsc --project src/main/tsconfig.json && tsc --project src/preload/tsconfig.json", "preview": "vite preview", "start": "npm run build && electron-forge start", "package": "npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "lint": "echo \"No linting configured\"", "clean": "<PERSON><PERSON>f dist dist-electron out"}, "keywords": [], "author": {"name": "jf", "email": "https://gitlab.hunantianxing.com/"}, "license": "MIT", "dependencies": {"electron-squirrel-startup": "^1.0.1", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/plugin-auto-unpack-natives": "^7.8.2", "@electron-forge/plugin-fuses": "^7.8.2", "@electron-toolkit/utils": "^4.0.0", "@electron/fuses": "^1.8.0", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "electron": "37.2.4", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6"}}