{"name": "fast-ocr", "productName": "fast-ocr", "version": "1.0.0", "description": "Fast OCR - 基于Electron和React的OCR应用", "main": "apps/main/dist/index.js", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "start": "npm run build && electron-forge start", "package": "npm run build && electron-forge package", "make": "npm run build && electron-forge make", "publish": "npm run build && electron-forge publish", "lint": "echo \"No linting configured\"", "clean": "rimraf apps/*/dist packages/*/dist out", "clean:all": "npm run clean && rimraf node_modules apps/*/node_modules packages/*/node_modules", "install:all": "npm install && npm run install:workspaces", "install:workspaces": "npm install --workspaces"}, "keywords": [], "author": {"name": "jf", "email": "https://gitlab.hunantianxing.com/"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.2", "@electron-forge/maker-deb": "^7.8.2", "@electron-forge/maker-rpm": "^7.8.2", "@electron-forge/maker-squirrel": "^7.8.2", "@electron-forge/maker-zip": "^7.8.2", "@electron-forge/plugin-auto-unpack-natives": "^7.8.2", "@electron-forge/plugin-fuses": "^7.8.2", "@electron/fuses": "^1.8.0", "electron": "37.2.4", "rimraf": "^6.0.1"}}