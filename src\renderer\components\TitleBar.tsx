import React from 'react'

interface TitleBarProps {
  title: string
  onMinimize: () => void
  onMaximize: () => void
  onClose: () => void
  isMaximized: boolean
}

const TitleBar: React.FC<TitleBarProps> = ({
  title,
  onMinimize,
  onMaximize,
  onClose,
  isMaximized
}) => {
  return (
    <div className="title-bar">
      <div className="title-bar-controls">
        <button
          className="title-bar-button close"
          onClick={onClose}
          title="关闭"
        />
        <button
          className="title-bar-button minimize"
          onClick={onMinimize}
          title="最小化"
        />
        <button
          className="title-bar-button maximize"
          onClick={onMaximize}
          title={isMaximized ? "还原" : "最大化"}
        />
      </div>
      <div className="title-bar-title">{title}</div>
      <div style={{ width: '68px' }} /> {/* 占位符，保持标题居中 */}
    </div>
  )
}

export default TitleBar
