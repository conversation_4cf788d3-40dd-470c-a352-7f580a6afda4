# Fast OCR

基于Electron和React的OCR应用，采用完全分离的架构设计。

## 🏗️ 项目架构

本项目采用**主进程与渲染进程完全分离**的架构设计：

```
fast-ocr/
├── apps/                    # 应用程序
│   ├── main/               # Electron主进程（独立构建）
│   │   ├── package.json    # 主进程独立依赖
│   │   ├── tsconfig.json   # 主进程TypeScript配置
│   │   └── index.ts        # 主进程入口
│   └── renderer/           # React渲染进程（独立构建）
│       ├── package.json    # 渲染进程独立依赖
│       ├── vite.config.ts  # Vite构建配置
│       ├── tsconfig.json   # 渲染进程TypeScript配置
│       └── src/            # React应用源码
├── packages/               # 共享包
│   ├── preload/           # Preload脚本（通信桥梁）
│   │   ├── package.json   # Preload独立依赖
│   │   └── index.ts       # 进程间通信API定义
│   └── shared/            # 共享类型和工具
│       ├── package.json   # 共享包依赖
│       ├── types/         # 共享类型定义
│       └── utils/         # 共享工具函数
└── scripts/               # 构建和开发脚本
    ├── dev.js            # 开发环境启动脚本
    ├── build.js          # 生产构建脚本
    └── dev-config.js     # 开发配置
```

## ✨ 架构特点

### 1. **完全分离的构建系统**
- 主进程和渲染进程拥有独立的`package.json`和`node_modules`
- 主进程使用TypeScript编译，渲染进程使用Vite构建
- 无共享依赖，避免版本冲突

### 2. **安全的进程通信**
- 仅通过Preload脚本实现进程间通信
- 启用`contextIsolation`和禁用`nodeIntegration`
- 清晰定义的API接口作为通信契约

### 3. **现代化开发体验**
- React渲染进程支持热重载
- TypeScript全栈支持
- Workspace管理多包依赖

## 🚀 快速开始

### 安装依赖

```bash
# 安装根目录和所有workspace的依赖
npm run install:all
```

### 开发模式

```bash
# 启动开发环境（自动启动所有服务）
npm run dev
```

开发模式会自动：
1. 启动React开发服务器（端口5173）
2. 启动主进程和Preload脚本的监听编译
3. 启动Electron应用

### 生产构建

```bash
# 构建所有包
npm run build

# 打包应用
npm run package

# 制作安装包
npm run make
```

## 📦 包管理

项目使用npm workspaces管理多包依赖：

```bash
# 为特定包安装依赖
npm install <package> --workspace=apps/renderer

# 清理所有构建产物
npm run clean

# 清理所有依赖和构建产物
npm run clean:all
```

## 🔧 开发指南

### 添加新的共享类型
在`packages/shared/types/`中定义，然后在各个包中通过路径别名引用。

### 扩展进程间通信API
在`packages/preload/index.ts`中定义新的API方法，确保类型安全。

### 修改构建配置
- 主进程：修改`apps/main/tsconfig.json`
- 渲染进程：修改`apps/renderer/vite.config.ts`
- Preload：修改`packages/preload/tsconfig.json`

## 🛡️ 安全策略

- ✅ Context Isolation启用
- ✅ Node Integration禁用
- ✅ Sandbox模式可选
- ✅ 仅通过Preload脚本暴露安全API

## 📋 推荐IDE设置

- [VSCode](https://code.visualstudio.com/)
- [TypeScript](https://marketplace.visualstudio.com/items?itemName=ms-vscode.vscode-typescript-next)
- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
