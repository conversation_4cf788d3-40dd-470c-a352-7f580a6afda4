# Fast OCR

一个基于 Electron + React + TypeScript + Vite 的现代化 OCR（光学字符识别）桌面应用程序。

## 🚀 技术栈

- **前端框架**: React 18 + TypeScript
- **桌面应用**: Electron 37
- **构建工具**: Vite 7
- **开发工具**: TypeScript 5.8
- **打包工具**: Electron Forge

## 📁 项目结构

```
fast-ocr/
├── src/
│   ├── main/                 # Electron 主进程
│   │   ├── index.ts         # 主进程入口文件
│   │   └── tsconfig.json    # 主进程 TypeScript 配置
│   ├── preload/             # 预加载脚本
│   │   ├── index.ts         # 预加载脚本入口
│   │   └── tsconfig.json    # 预加载脚本 TypeScript 配置
│   ├── renderer/            # React 渲染进程
│   │   ├── components/      # React 组件
│   │   │   ├── TitleBar.tsx # 自定义标题栏
│   │   │   ├── MainContent.tsx # 主内容区域
│   │   │   └── StatusBar.tsx # 状态栏
│   │   ├── App.tsx          # 主应用组件
│   │   ├── main.tsx         # React 入口文件
│   │   ├── index.html       # HTML 模板
│   │   ├── index.css        # 全局样式
│   │   └── tsconfig.json    # 渲染进程 TypeScript 配置
│   └── shared/              # 共享代码
│       ├── types/           # 类型定义
│       │   └── index.ts     # 共享类型
│       └── utils/           # 工具函数
│           └── index.ts     # 共享工具函数
├── public/                  # 静态资源
├── dist-electron/           # 构建输出目录
│   ├── main/               # 主进程构建输出
│   ├── preload/            # 预加载脚本构建输出
│   └── renderer/           # 渲染进程构建输出
├── vite.config.ts          # Vite 配置
├── tsconfig.json           # 根 TypeScript 配置
├── forge.config.js         # Electron Forge 配置
└── package.json            # 项目配置
```

## 🛠️ 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建应用

```bash
npm run build
```

### 启动应用

```bash
npm start
```

### 打包应用

```bash
npm run package
```

### 制作安装包

```bash
npm run make
```

### 清理构建文件

```bash
npm run clean
```

## 🏗️ 架构特点

### 前后端分离
- **主进程 (Main Process)**: 负责应用生命周期管理、窗口创建、系统 API 调用
- **渲染进程 (Renderer Process)**: 运行 React 应用，负责用户界面
- **预加载脚本 (Preload Script)**: 安全地暴露 Electron API 给渲染进程

### 类型安全
- 全项目使用 TypeScript，提供完整的类型检查
- 共享类型定义，确保前后端数据结构一致
- 严格的 TypeScript 配置，提高代码质量

### 现代化构建
- 使用 Vite 作为前端构建工具，提供快速的开发体验
- 支持热重载和快速构建
- 优化的生产构建，包含代码分割和压缩

### 安全性
- 启用上下文隔离 (Context Isolation)
- 禁用 Node.js 集成在渲染进程中
- 通过预加载脚本安全地暴露 API

## 🔧 配置说明

### TypeScript 配置
- 根目录 `tsconfig.json`: 项目级别的 TypeScript 配置
- 各模块独立的 `tsconfig.json`: 针对不同模块的特定配置

### Vite 配置
- 支持 React 和 TypeScript
- 配置路径别名，简化导入
- 集成 Electron 插件

### Electron Forge 配置
- 支持多平台打包
- 配置应用图标和元数据
- 自动代码签名和更新

## 📦 构建流程

1. **渲染进程构建**: 使用 Vite 构建 React 应用
2. **主进程构建**: 使用 TypeScript 编译器构建主进程代码
3. **预加载脚本构建**: 使用 TypeScript 编译器构建预加载脚本
4. **应用打包**: 使用 Electron Forge 打包最终应用

## 🎯 功能特性

- ✅ 现代化的用户界面
- ✅ 跨平台支持 (Windows, macOS, Linux)
- ✅ 自定义标题栏
- ✅ 文件对话框集成
- ✅ 应用状态管理
- ✅ 开发者工具集成
- ✅ 热重载开发体验

## 🔮 未来计划

- [ ] OCR 功能集成
- [ ] 多语言支持
- [ ] 主题切换
- [ ] 插件系统
- [ ] 自动更新

## 📄 许可证

MIT License

## 👨‍💻 开发者

jf - [https://gitlab.hunantianxing.com/](https://gitlab.hunantianxing.com/)


太棒了！将渲染进程交给 React 和 Vite 来处理是目前 Electron 开发中**最高效、最现代化的方案之一**。

这个组合的优势非常明显：

*   **极速的开发体验**：Vite 的 HMR（热模块替换）速度无与伦比，你在 React 组件中的任何修改几乎都能瞬间反映在 Electron 窗口中，无需刷新。
*   **现代前端生态**：可以无缝使用 React 的所有生态，如 Hooks、Redux、React Router 等，并享受 Vite 带来的开箱即用的 TypeScript, JSX, CSS Modules 支持。
*   **清晰的职责分离**：主进程（Electron）和渲染进程（React/Vite）的开发和构建完全解耦，项目结构更清晰，维护性更强。

下面，我将为你详细解释如何整合这套技术栈，以及推荐的项目结构。

---

### 核心思想：两个独立的世界

关键在于理解：**主进程和渲染进程是两个独立的应用**，它们在开发时并行运行：

1.  **渲染进程 (React App)**：由 Vite 的开发服务器 (`vite dev`) 启动，运行在 `http://localhost:5173` (或其他端口) 上。它就是一个标准的 Web 应用。
2.  **主进程 (Electron App)**：由 `electron .` 启动。它的工作不再是加载本地的 `index.html` 文件，而是**加载 Vite Dev Server 的 URL**。

当项目打包构建时，Vite 会将 React 应用构建成静态文件，然后 Electron 再去加载这些构建好的本地文件。

---

### 推荐的项目结构（Monorepo-like 结构）

为了实现职责分离，我们不再将所有代码都放在一个 `src` 目录中，而是创建两个主要的子目录，每个目录都有自己的 `package.json`（可选，但推荐）和配置文件。

```
my-electron-react-app/
├── package.json              <-- 根 package.json，用于管理整个项目
├── electron/                 <-- 主进程和预加载脚本相关
│   ├── package.json          <-- 只管理 electron 相关的依赖
│   ├── tsconfig.json         <-- 主进程的 TS 配置
│   └── src/
│       ├── main.ts           <-- 主进程入口
│       └── preload.ts        <-- 预加载脚本
│
└── renderer/                 <-- 渲染进程 (React + Vite)
    ├── package.json          <-- React 和 Vite 相关的依赖
    ├── tsconfig.json         <-- React 的 TS 配置 (由 Vite 生成)
    ├── vite.config.ts        <-- Vite 配置文件
    ├── public/
    └── src/
        ├── App.tsx
        ├── main.tsx
        └── ... (其他 React 组件)
```

**为什么是这种结构？**
*   **依赖隔离**：`electron/` 和 `renderer/` 的依赖是分开的。React 不需要知道 Electron 的存在，反之亦然。这让升级和维护变得非常简单。
*   **配置独立**：主进程（Node.js 环境）和渲染进程（浏览器环境）的 `tsconfig.json` 配置需求不同，分开管理更清晰。
*   **构建解耦**：可以独立地构建主进程代码和渲染进程的 UI。

---

### 实践指南：一步步搭建项目

#### 第1步：创建项目根目录和总控 `package.json`

```bash
mkdir my-electron-react-app
cd my-electron-react-app
npm init -y

# 安装一个工具来同时运行多个命令
npm install --save-dev concurrently
```

修改根目录的 `package.json`，添加脚本来协调两个子项目：

```json
// my-electron-react-app/package.json
{
  "name": "my-electron-react-app",
  "version": "1.0.0",
  "main": "electron/dist/main.js", // 指向构建后的主进程文件
  "scripts": {
    "dev": "concurrently \"npm run dev -w renderer\" \"npm run dev -w electron\"",
    "build": "npm run build -w renderer && npm run build -w electron",
    "start": "electron ."
  },
  "devDependencies": {
    "concurrently": "^8.2.2",
    "electron": "^28.0.0"
  },
  "workspaces": [ // 告诉 npm 这是个 workspace 项目
    "electron",
    "renderer"
  ]
}
```
**解释**：
*   `workspaces`: 启用 NPM Workspaces，可以更方便地管理子项目。
*   `dev` 脚本: 使用 `concurrently` 同时运行 `renderer` 和 `electron` 的 `dev` 脚本。
*   `-w renderer`: NPM Workspaces 的语法，表示在 `renderer` 工作区内运行命令。

#### 第2步：创建渲染进程 (React + Vite)

```bash
# 在项目根目录运行
# 使用 Vite 官方脚手架在 renderer 目录创建项目
npm create vite@latest renderer -- --template react-ts

# 进入 renderer 目录安装依赖
cd renderer
npm install
cd ..
```
Vite 会为你生成一个完整的 React + TS 项目，包括 `package.json`, `vite.config.ts` 等。

#### 第3步：创建主进程 (Electron)

```bash
# 在项目根目录
mkdir -p electron/src
cd electron

# 初始化主进程的 package.json
npm init -y

# 安装 Electron 和 TS 相关依赖
npm install --save-dev typescript @types/node

cd ..
```
现在，创建主进程的 `tsconfig.json` 和代码文件。

1.  **`electron/tsconfig.json`**
    ```json
    {
      "compilerOptions": {
        "target": "ES2020",
        "module": "commonjs",
        "outDir": "./dist",
        "rootDir": "./src",
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true
      },
      "include": ["src/**/*"]
    }
    ```

2.  **`electron/src/preload.ts`** (内容和之前类似)
    ```typescript
    import { contextBridge } from 'electron';

    contextBridge.exposeInMainWorld('myAPI', {
      // 示例 API
      platform: process.platform,
    });
    ```

3.  **`electron/src/main.ts` (关键修改！)**

    这里是整合的核心。我们需要根据环境（开发/生产）来决定加载 URL 还是本地文件。

    ```typescript
    import { app, BrowserWindow } from 'electron';
    import path from 'path';

    // Vite 开发服务器的 URL
    const VITE_DEV_SERVER_URL = 'http://localhost:5173';

    function createWindow() {
      const mainWindow = new BrowserWindow({
        width: 800,
        height: 600,
        webPreferences: {
          preload: path.join(__dirname, 'preload.js'), // 关键！
        },
      });

      if (process.env.NODE_ENV === 'development') {
        // 开发模式下，加载 Vite Dev Server
        mainWindow.loadURL(VITE_DEV_SERVER_URL);
        // 打开开发者工具
        mainWindow.webContents.openDevTools();
      } else {
        // 生产模式下，加载打包后的 HTML 文件
        // `__dirname` 是 `electron/dist`
        // `../renderer/dist/index.html` 是相对于 `electron/dist` 的路径
        mainWindow.loadFile(path.join(__dirname, '../renderer/dist/index.html'));
      }
    }

    app.whenReady().then(createWindow);
    // ... 其他生命周期事件
    ```

#### 第4步：配置子项目的 `package.json` 脚本

1.  **`renderer/package.json`**: Vite 生成的 `dev` 和 `build` 脚本通常是好的，无需修改。
    ```json
    // renderer/package.json
    "scripts": {
      "dev": "vite",
      "build": "tsc && vite build",
      "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
      "preview": "vite preview"
    },
    ```

2.  **`electron/package.json`**: 添加 `dev` 和 `build` 脚本。
    ```json
    // electron/package.json
    {
      "name": "electron-main",
      "version": "1.0.0",
      "main": "dist/main.js",
      "scripts": {
        "build": "tsc",
        "dev": "npm run build && cross-env NODE_ENV=development electron ."
      },
      "devDependencies": {
        // 我们需要 cross-env 来设置环境变量
        "cross-env": "^7.0.3", 
        "typescript": "...",
        "@types/node": "..."
      }
    }
    ```
    *   **别忘了安装 `cross-env`**: `npm install --save-dev cross-env -w electron`

#### 第5步：在 React 中使用 Preload API

为了让 TypeScript 知道 `window` 对象上的 `myAPI`，你需要在 `renderer` 项目中创建一个类型声明文件。

1.  创建 **`renderer/src/electron.d.ts`**
    ```typescript
    // renderer/src/electron.d.ts
    export interface IMyAPI {
      platform: string;
    }

    declare global {
      interface Window {
        myAPI: IMyAPI;
      }
    }
    ```

2.  修改 **`renderer/src/App.tsx`** 来调用它
    ```tsx
    import './App.css'

    function App() {
      return (
        <>
          <h1>Vite + React in Electron!</h1>
          <p>
            Welcome from platform: {window.myAPI.platform}
          </p>
        </>
      )
    }

    export default App
    ```

---

### 如何运行和构建？

现在，所有的协调工作都在根目录的 `package.json` 中完成。

**开发时**：

```bash
# 在项目根目录运行
npm run dev
```
`concurrently` 会：
1.  启动 Vite 开发服务器 (`npm run dev -w renderer`)。
2.  编译主进程代码并启动 Electron 应用 (`npm run dev -w electron`)，它会自动加载 Vite 的 URL。
3.  现在修改 React 组件，窗口内容会立即更新！

**构建和打包**：

1.  **构建**
    ```bash
    # 在项目根目录运行
    npm run build
    ```
    这会：
    *   在 `renderer/dist` 中生成优化后的静态 Web 应用。
    *   在 `electron/dist` 中生成编译后的主进程 JS 文件。

2.  **打包成可执行文件**
    这一步通常使用 `electron-builder` 或 `electron-forge`。你需要将它作为根项目的 dev a依赖安装。

    ```bash
    # 在根目录安装
    npm install --save-dev electron-builder
    ```
    然后在根 `package.json` 中添加 `build` 配置和打包脚本。这部分配置较多，但基本思路是告诉 `electron-builder` 去哪里找构建好的文件。

### 总结

使用 Vite + React 处理渲染进程是现代 Electron 开发的黄金标准。

**关键点回顾**：

1.  **项目结构分离**：使用 `electron` 和 `renderer` 两个子目录，保持职责清晰。
2.  **开发流程**：`concurrently` 同时运行 Vite Dev Server 和 Electron 主进程。主进程加载 Vite 的 URL。
3.  **生产流程**：先用 Vite 构建 React 应用，再用 `tsc` 编译主进程，最后用 `electron-builder` 等工具将所有产物打包到一起。
4.  **通信桥梁**：`preload.ts` 仍然是主进程和渲染进程之间安全通信的唯一桥梁。

这套架构虽然初始设置稍复杂，但一旦搭建完成，你将获得无与伦比的开发效率和项目可维护性。